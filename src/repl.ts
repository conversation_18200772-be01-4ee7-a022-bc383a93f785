import { NestFactory } from '@nestjs/core';
import { ReplModule } from './modules/repl/repl.module';

async function bootstrap() {
    try {
        // Create REPL application
        const replApp = await NestFactory.create(ReplModule, {
            logger: ['error', 'warn', 'log'],
        });

        // Enable CORS for the web interface
        replApp.enableCors({
            origin: '*',
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
            credentials: true,
        });

        // Start REPL server
        const port = parseInt(process.env.REPL_PORT || '3001', 10);
        await replApp.listen(port);

        // eslint-disable-next-line no-console
        console.log(
            `🚀 Kashf REPL GUI is running on: http://localhost:${port}`,
        );
        // eslint-disable-next-line no-console
        console.log(
            '📝 Open the URL in your browser to access the interactive REPL',
        );

        // Try to create main app context in background
        try {
            const { AppModule } = await import('./app.module');
            const app = await NestFactory.create(AppModule, { logger: false });
            global.app = app;
            // eslint-disable-next-line no-console
            console.log('✅ Main application context loaded successfully');
        } catch (error) {
            // eslint-disable-next-line no-console
            console.warn('⚠️  Could not load main app context:', error.message);
            // eslint-disable-next-line no-console
            console.log(
                '🔧 REPL will work with basic JavaScript functionality',
            );
        }
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error('❌ Failed to start REPL:', error);
        process.exit(1);
    }
}

bootstrap();
