import { Injectable } from '@nestjs/common';
import * as util from 'util';
import * as vm from 'vm';

export interface IReplResult {
    success: boolean;
    result?: any;
    error?: string;
    output?: string;
    type?: string;
}

@Injectable()
export class ReplService {
    private context: vm.Context;
    private replServer: any;

    constructor() {
        this.initializeContext();
    }

    private initializeContext(): void {
        try {
            // Create a new context with global objects
            this.context = vm.createContext({
                console: {
                    log: (...args) => this.captureOutput('log', args),
                    error: (...args) => this.captureOutput('error', args),
                    warn: (...args) => this.captureOutput('warn', args),
                    info: (...args) => this.captureOutput('info', args),
                },
                require,
                process,
                Buffer,
                global,
                __dirname,
                __filename,
                setTimeout,
                setInterval,
                clearTimeout,
                clearInterval,
                setImmediate,
                clearImmediate,
            });

            // Add NestJS app context dynamically (check at runtime)
            Object.defineProperty(this.context, 'app', {
                get: () => global.app,
                enumerable: true,
                configurable: true,
            });

            // Add common services and modules (simple dynamic functions)
            this.context.getService = (serviceClass: any) => {
                if (!global.app) {
                    throw new Error('NestJS app context not available');
                }
                try {
                    return global.app.get(serviceClass);
                } catch (error) {
                    throw new Error(
                        `Service not found: ${serviceClass.name || serviceClass}`,
                    );
                }
            };

            // Add TypeORM connection if available
            this.context.getConnection = () => {
                if (!global.app) {
                    throw new Error('NestJS app context not available');
                }
                try {
                    const { DataSource } = require('typeorm');
                    return global.app.get(DataSource);
                } catch (error) {
                    throw new Error('TypeORM DataSource not available');
                }
            };

            // Add entity manager
            this.context.getEntityManager = () => {
                try {
                    const connection = this.context.getConnection();
                    return connection.manager;
                } catch (error) {
                    throw new Error('EntityManager not available');
                }
            };

            // Add repository helper
            this.context.getRepository = (entity: any) => {
                try {
                    const connection = this.context.getConnection();
                    return connection.getRepository(entity);
                } catch (error) {
                    throw new Error(
                        `Repository for ${entity.name || entity} not found`,
                    );
                }
            };

            // Add utility functions
            this.context.inspect = util.inspect;
            this.context.help = () => {
                const hasApp = !!global.app;
                return `
Kashf REPL - Available Commands:

${hasApp ? '✅ NestJS Application Context Available' : '❌ NestJS Application Context Not Available'}

Basic Functions:
- help(): Show this help message
- inspect(obj): Inspect an object with detailed formatting
- console.log/error/warn/info(): Output to console

${
    hasApp
        ? `
NestJS Functions:
- app: NestJS application instance
- getService(ServiceClass): Get a service from the DI container
- getConnection(): Get TypeORM DataSource
- getEntityManager(): Get TypeORM EntityManager
- getRepository(EntityClass): Get repository for an entity

Example usage:
// Get a service (replace with actual service names from your app)
const configService = getService('ConfigService');

// Database operations
const entityManager = getEntityManager();
const users = await entityManager.query('SELECT * FROM users LIMIT 5');

// Repository pattern
// const userRepo = getRepository(User);
// const user = await userRepo.findOne({ where: { id: 1 } });

// Inspect results
inspect(users);
`
        : `
Limited Mode:
Only basic JavaScript functionality is available.
The main application context could not be loaded.
`
}

Tips:
- Use 'await' for async operations
- Results are automatically displayed
- Use Ctrl+Enter to execute code
                `.trim();
            };
        } catch (error) {
            console.error('Failed to initialize REPL context:', error);
        }
    }

    private outputBuffer: string[] = [];

    private captureOutput(type: string, args: any[]): void {
        const message = args
            .map((arg) =>
                typeof arg === 'object'
                    ? util.inspect(arg, { depth: 2, colors: false })
                    : String(arg),
            )
            .join(' ');
        this.outputBuffer.push(`[${type.toUpperCase()}] ${message}`);
    }

    async executeCode(code: string): Promise<IReplResult> {
        this.outputBuffer = []; // Clear previous output

        try {
            // Add safety checks
            if (!code || typeof code !== 'string') {
                throw new Error('Invalid code input');
            }

            // Wrap the code in an async function to support await with better error handling
            const wrappedCode = `
                (async () => {
                    try {
                        ${code}
                    } catch (innerError) {
                        console.error('Execution error:', innerError.message);
                        throw innerError;
                    }
                })()
            `;

            // Add promise timeout wrapper to prevent hanging
            const executeWithTimeout = new Promise(async (resolve, reject) => {
                const timeoutId = setTimeout(() => {
                    reject(new Error('Code execution timed out after 10 seconds'));
                }, 10000);

                try {
                    const result = await vm.runInContext(wrappedCode, this.context, {
                        timeout: 9000, // VM timeout slightly less than promise timeout
                        displayErrors: true,
                    });
                    clearTimeout(timeoutId);
                    resolve(result);
                } catch (error) {
                    clearTimeout(timeoutId);
                    reject(error);
                }
            });

            const result = await executeWithTimeout;
            const output = this.outputBuffer.join('\n');

            return {
                success: true,
                result,
                output: output || undefined,
                type: typeof result,
            };
        } catch (error) {
            const output = this.outputBuffer.join('\n');

            // Log the error for debugging but don't let it crash the server
            console.error('REPL execution error:', error);

            return {
                success: false,
                error: error.message || String(error),
                output: output || undefined,
            };
        }
    }

    async getContextInfo(): Promise<any> {
        try {
            const contextKeys = Object.keys(this.context).filter(
                (key) =>
                    ![
                        'console',
                        'require',
                        'process',
                        'Buffer',
                        'global',
                        '__dirname',
                        '__filename',
                    ].includes(key),
            );

            return {
                availableServices: global.app
                    ? 'NestJS app context available'
                    : 'No app context',
                contextKeys,
                helpAvailable: true,
            };
        } catch (error) {
            return {
                error: error.message,
            };
        }
    }
}
